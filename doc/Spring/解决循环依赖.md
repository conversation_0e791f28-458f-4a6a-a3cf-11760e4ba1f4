### springboot如何解决循环依赖的问题？

在 Spring Boot 中，循环依赖指的是两个或多个 Bean 之间相互引用（例如 A 依赖 B，B 又依赖 A）。Spring 容器通过 **三级缓存机制** 解决了大部分循环依赖问题，具体原理和处理方式如下：


### 1. 循环依赖的场景
循环依赖通常分为三种情况：
- **构造器注入循环依赖**：无法解决，会抛出 `BeanCurrentlyInCreationException`。
- **setter 注入/字段注入循环依赖**：可以通过 Spring 的三级缓存机制解决。


### 2. 三级缓存机制（核心解决方案）
Spring 通过三个缓存（Map）实现循环依赖的处理，分别是：
1. **一级缓存（singletonObjects）**：存储完全初始化完成的单例 Bean，直接供外部使用。
2. **二级缓存（earlySingletonObjects）**：存储提前曝光的“半成品” Bean（已实例化但未完成属性注入和初始化）。
3. **三级缓存（singletonFactories）**：存储 Bean 的工厂对象（`ObjectFactory`），用于生成“半成品” Bean 的代理对象（如果需要）。


### 3. 解决流程（以 A 依赖 B，B 依赖 A 为例）
1. **实例化 A**：Spring 先创建 A 的实例（调用构造器），但不注入属性，然后将 A 的工厂对象（`ObjectFactory`）放入三级缓存。
2. **A 依赖 B**：A 需要注入 B，此时 Spring 发现 B 未创建，开始实例化 B。
3. **实例化 B**：创建 B 的实例，将 B 的工厂对象放入三级缓存，然后 B 需要注入 A。
4. **B 依赖 A**：Spring 从三级缓存中获取 A 的工厂对象，生成 A 的“半成品” Bean（如果 A 需要代理，此时会生成代理对象），并将 A 从三级缓存移到二级缓存。
5. **完成 B 的初始化**：B 注入 A 后，完成属性注入和初始化，B 被放入一级缓存。
6. **完成 A 的初始化**：A 从一级缓存中获取 B 并注入，最终 A 完成初始化，被放入一级缓存。


### 4. 无法解决的情况
- **构造器注入循环依赖**：因为构造器注入要求在实例化阶段就传入依赖，而此时依赖对象可能尚未实例化，三级缓存无法介入。
    - 解决方案：将构造器注入改为 setter 注入或字段注入。
- **原型（Prototype）Bean 循环依赖**：Spring 不缓存原型 Bean，每次获取都会新建，因此无法解决循环依赖。
    - 解决方案：避免原型 Bean 之间的循环依赖，或改为单例 Bean。


### 5. 总结
Spring Boot 解决循环依赖的核心是 **三级缓存机制**，通过提前曝光“半成品” Bean，允许依赖对象在初始化完成前被引用。但需注意：
- 仅支持 **单例 Bean** 的 setter/字段注入循环依赖。
- 构造器注入和原型 Bean 的循环依赖无法解决，需通过调整注入方式或Bean 作用域规避。

---

### 构造器注入循环依赖，setter 注入 / 字段注入循环依赖 //请分别举例说明

以下分别通过代码示例说明**构造器注入循环依赖**和**setter注入/字段注入循环依赖**的场景，以及 Spring 对它们的不同处理结果。


### 一、构造器注入循环依赖（无法解决）
构造器注入要求在 Bean 实例化阶段（调用构造器）就传入依赖对象。如果两个 Bean 相互通过构造器依赖，会导致 Spring 无法完成实例化，最终抛出 `BeanCurrentlyInCreationException`。

#### 示例代码：
```java
// Bean A
@Component
public class A {
    private B b;

    // 构造器注入 B
    @Autowired
    public A(B b) {
        this.b = b;
    }
}

// Bean B
@Component
public class B {
    private A a;

    // 构造器注入 A
    @Autowired
    public B(A a) {
        this.a = a;
    }
}
```

#### 运行结果：
启动 Spring Boot 应用时会报错，错误信息类似：
```
org.springframework.beans.factory.BeanCurrentlyInCreationException: 
Error creating bean with name 'a': Requested bean is currently in creation: Is there an unresolvable circular reference?
```

#### 原因：
- Spring 实例化 A 时，发现需要 B，于是去实例化 B。
- 实例化 B 时，发现需要 A，但此时 A 还处于“正在创建”状态（未完成实例化），无法提供给 B，导致循环依赖无法解决。


### 二、setter 注入循环依赖（可解决）
setter 注入是在 Bean 实例化（构造器调用完成）之后，通过 setter 方法注入依赖。此时 Spring 可以通过三级缓存提前暴露“半成品”Bean，解决循环依赖。

#### 示例代码：
```java
// Bean A
@Component
public class A {
    private B b;

    // setter 注入 B
    @Autowired
    public void setB(B b) {
        this.b = b;
    }
}

// Bean B
@Component
public class B {
    private A a;

    // setter 注入 A
    @Autowired
    public void setA(A a) {
        this.a = a;
    }
}
```

#### 运行结果：
应用正常启动，A 和 B 成功完成依赖注入。

#### 原理：
1. Spring 先实例化 A（调用无参构造器，此时 A 是“半成品”，未注入 B），并将 A 的工厂对象放入三级缓存。
2. A 需要通过 setter 注入 B，Spring 发现 B 未创建，于是实例化 B（同样是“半成品”，未注入 A），并将 B 的工厂对象放入三级缓存。
3. B 需要通过 setter 注入 A，Spring 从三级缓存中获取 A 的“半成品”，注入到 B 中，B 完成初始化并放入一级缓存。
4. A 从一级缓存中获取已完成的 B，通过 setter 注入，最终 A 完成初始化并放入一级缓存。


### 三、字段注入循环依赖（可解决）
字段注入（直接在字段上使用 `@Autowired`）本质上是 Spring 自动生成 setter 方法进行注入，因此和 setter 注入一样，支持循环依赖。

#### 示例代码：
```java
// Bean A
@Component
public class A {
    // 字段注入 B
    @Autowired
    private B b;
}

// Bean B
@Component
public class B {
    // 字段注入 A
    @Autowired
    private A a;
}
```

#### 运行结果：
应用正常启动，A 和 B 成功完成依赖注入。

#### 原理：
字段注入的底层逻辑和 setter 注入一致，都是在 Bean 实例化后进行依赖注入，因此可以利用三级缓存解决循环依赖。


### 总结
| 注入方式       | 循环依赖是否可解决 | 核心原因                                                                 |
|----------------|--------------------|--------------------------------------------------------------------------|
| 构造器注入     | 不可解决           | 实例化阶段就需要依赖，此时依赖对象尚未创建，三级缓存无法介入。           |
| setter 注入    | 可解决             | 实例化后注入依赖，Spring 可通过三级缓存提前暴露“半成品”Bean 打破循环。   |
| 字段注入       | 可解决             | 底层等价于 setter 注入，依赖注入发生在实例化之后，支持循环依赖处理。     |

实际开发中，应尽量避免循环依赖（设计上的不合理），若无法避免，建议使用 setter 注入或字段注入（需注意字段注入的滥用问题）。

---

### 为什么这里没有二级缓存什么事情


在循环依赖的处理流程中，二级缓存（`earlySingletonObjects`）并非“没用”，而是在特定场景下发挥关键作用，主要用于**缓存提前曝光的“半成品”Bean（可能是原始对象或代理对象）**，避免重复生成对象（尤其是代理对象）。

下面结合具体场景说明二级缓存的作用，以及它为什么在基础示例中容易被忽略：


### 一、二级缓存的核心作用
二级缓存是**“半成品Bean的临时缓存”**，存储的是已经从三级缓存中“取出并生成”的对象（可能是原始对象，也可能是代理对象）。它的存在主要是为了：
1. **避免重复生成代理对象**：如果一个Bean需要被代理（例如AOP增强），三级缓存中的`ObjectFactory`会生成代理对象。二级缓存会缓存这个代理对象，后续其他Bean依赖它时，直接从二级缓存获取，无需再次执行`ObjectFactory`的逻辑。
2. **提升性能**：减少三级缓存中工厂对象的重复调用，尤其是代理对象生成的成本较高时。


### 二、为什么基础示例中感觉不到二级缓存的存在？
在之前的简单示例（如普通Bean的setter注入循环依赖）中，Bean不需要代理，此时三级缓存的`ObjectFactory`逻辑很简单（直接返回原始对象），二级缓存的作用不明显，导致流程看起来“绕过”了二级缓存。

具体来说：  
当Bean不需要代理时，三级缓存中`ObjectFactory`的`getObject()`方法直接返回Bean的原始实例。此时从三级缓存获取对象后，会直接放入二级缓存，但由于后续没有其他Bean再次依赖它，所以二级缓存的存在不会被感知。


### 三、必须用到二级缓存的场景：Bean需要代理（如AOP）
当循环依赖的Bean需要被代理（例如添加了`@Transactional`、`@Async`等注解），二级缓存的作用会非常明显。

#### 示例流程（A依赖B，B依赖A，且A需要被代理）：
1. 实例化A（原始对象），将生成A的`ObjectFactory`放入三级缓存（`singletonFactories`）。
2. A需要注入B，开始实例化B，将B的`ObjectFactory`放入三级缓存。
3. B需要注入A：
    - 从三级缓存获取A的`ObjectFactory`，调用`getObject()`生成A的**代理对象**（因为A需要AOP增强）。
    - 将A的代理对象从三级缓存移到**二级缓存**（`earlySingletonObjects`），避免后续重复生成代理。
4. B注入A的代理对象后，完成初始化，放入一级缓存（`singletonObjects`）。
5. A从一级缓存获取B，完成注入后，自己的初始化完成。此时A的最终对象是二级缓存中的代理对象，将其移到一级缓存。


### 四、三级缓存的协作关系
三级缓存的调用顺序是**先查一级 → 再查二级 → 最后查三级**，三者协作如下：
- 一级缓存：存储完全初始化的成熟Bean（最终可用的对象）。
- 二级缓存：存储提前曝光的“半成品”Bean（可能是代理对象），避免重复生成。
- 三级缓存：存储生成“半成品”Bean的工厂（`ObjectFactory`），用于在需要时生成对象（尤其是代理对象）。

简单说：**三级缓存负责“生成”对象，二级缓存负责“缓存”生成后的对象，一级缓存负责“存储最终结果”**。


### 总结
二级缓存并非多余，它是三级缓存机制中不可或缺的一环：
- 在**无代理的循环依赖**中，二级缓存的作用被弱化（仅临时缓存原始对象），因此容易被忽略。
- 在**有代理的循环依赖**中，二级缓存是核心，用于缓存代理对象，避免重复生成，保证依赖注入的对象一致性。

Spring通过三级缓存的配合，既解决了循环依赖，又保证了代理对象的正确生成和复用。

---

### 什么样的bean需要代理对象

在 Spring 中，Bean 需要生成代理对象通常与 **AOP（面向切面编程）** 相关，目的是在不修改原始 Bean 代码的情况下，为其添加额外功能（如事务管理、日志记录、权限校验等）。以下是需要生成代理对象的典型场景：


### 1. **使用声明式事务（`@Transactional`）**
当 Bean 的方法被 `@Transactional` 注解标记时，Spring 会通过 AOP 为该 Bean 生成代理对象，以实现事务的开启、提交、回滚等操作。  
**示例**：
```java
@Service
public class UserService {
    @Transactional  // 触发代理生成
    public void saveUser() {
        // 业务逻辑
    }
}
```
- 代理对象会在调用 `saveUser()` 前后插入事务管理逻辑，原始 `UserService` 对象本身不会被修改。


### 2. **使用 Spring AOP 自定义切面（`@Aspect`）**
当通过 `@Aspect` 定义切面，并对目标 Bean 的方法进行增强（如 `@Before`、`@After`、`@Around` 等）时，Spring 会为目标 Bean 生成代理对象，以执行切面逻辑。  
**示例**：
```java
@Aspect
@Component
public class LogAspect {
    @Before("execution(* com.example.service.*Service.*(..))")  // 切入点
    public void logBefore() {
        System.out.println("方法执行前打印日志");
    }
}

@Service
public class OrderService {  // 被切面增强，会生成代理对象
    public void createOrder() {
        // 业务逻辑
    }
}
```
- `OrderService` 的代理对象会在 `createOrder()` 执行前先调用 `logBefore()` 方法。


### 3. **异步方法（`@Async`）**
当方法被 `@Async` 注解标记时，Spring 会生成代理对象，将方法的执行提交到异步线程池，实现异步调用。  
**示例**：
```java
@Service
public class AsyncService {
    @Async  // 触发代理生成
    public void asyncTask() {
        // 异步执行的逻辑
    }
}
```
- 代理对象会拦截 `asyncTask()` 调用，将其封装为异步任务，而非直接执行原始方法。


### 4. **缓存注解（`@Cacheable`、`@CachePut` 等）**
使用 Spring Cache 相关注解（如 `@Cacheable`）时，代理对象会拦截方法调用，实现缓存的读取、写入、失效等逻辑。  
**示例**：
```java
@Service
public class ProductService {
    @Cacheable(value = "products", key = "#id")  // 触发代理生成
    public Product getProduct(Long id) {
        // 查询数据库的逻辑
        return product;
    }
}
```
- 代理对象会先检查缓存中是否有数据，若有则直接返回，避免重复查询数据库。


### 5. **其他 AOP 相关注解或接口**
- **`@PreAuthorize`、`@PostAuthorize`**（Spring Security 中的权限校验）：代理对象会在方法执行前后验证权限。
- **实现 `InitializingBean`、`DisposableBean` 等接口**：虽然这些接口本身不直接生成代理，但如果结合 AOP 增强，仍可能触发代理。


### 代理对象的生成方式
Spring 会根据场景自动选择代理方式：
- **JDK 动态代理**：当目标 Bean 实现了接口时，默认使用 JDK 动态代理（基于接口的代理）。
- **CGLIB 代理**：当目标 Bean 没有实现接口时，使用 CGLIB 代理（基于类的继承代理）。  
  （可通过 `spring.aop.proxy-target-class=true` 强制使用 CGLIB 代理）


### 总结
Bean 需要生成代理对象的核心原因是 **需要通过 AOP 增强其功能**，常见场景包括事务管理、日志切面、异步执行、缓存控制等。代理对象的作用是在不侵入原始 Bean 代码的前提下，拦截方法调用并插入额外逻辑，这是 Spring AOP 实现的核心机制。

---

### 已实例化但未完成属性注入和初始化 //不甚理解这句话，对于bean来说，什么叫实例化，什么叫初始化，什么叫未完成属性注入

在 Spring 中，Bean 的创建过程可以拆解为 **实例化（Instantiation）**、**属性注入（Populate）**、**初始化（Initialization）** 三个核心阶段。理解这三个阶段的区别，就能明白“已实例化但未完成属性注入和初始化”的含义。


### 一、实例化（Instantiation）：创建对象的“壳子”
**实例化**是 Bean 生命周期的第一步，指的是**通过构造器创建对象的原始实例**（内存中分配空间，调用构造器生成对象），但此时对象的属性（字段）尚未赋值，处于“空壳”状态。

#### 举例：
```java
public class UserService {
    private UserDao userDao; // 未赋值
    private String appName;  // 未赋值

    // 构造器（实例化时调用）
    public UserService() {
        System.out.println("UserService 构造器被调用（实例化）");
    }
}
```
- 实例化后，`userDao` 和 `appName` 都是默认值（`null`），对象仅在内存中存在，但无法正常使用（缺少必要依赖）。


### 二、属性注入（Populate）：给对象“填内容”
**属性注入**是在实例化之后，Spring 会根据配置（如 `@Autowired`、XML 配置等），将该 Bean 依赖的其他对象或值**注入到其字段中**。

#### 举例：
```java
@Service
public class UserService {
    @Autowired  // 属性注入
    private UserDao userDao; 
    
    @Value("${app.name}")  // 属性注入
    private String appName;

    // 构造器（实例化时调用）
    public UserService() {
        System.out.println("UserService 构造器被调用（实例化）");
    }
}
```
- 实例化后，Spring 会通过 `@Autowired` 找到 `UserDao` 的实例，注入到 `userDao` 字段；
- 通过 `@Value` 读取配置文件，将值注入到 `appName` 字段；
- **未完成属性注入**：指的是这个阶段尚未执行，字段仍为默认值（如 `userDao` 还是 `null`）。


### 三、初始化（Initialization）：给对象“做最后的加工”
**初始化**是在属性注入完成后，Spring 对 Bean 进行的“最终处理”，确保对象可以正常使用。常见操作包括：
1. 执行自定义初始化方法（如 `@PostConstruct` 注解的方法、实现 `InitializingBean` 接口的 `afterPropertiesSet()` 方法）；
2. AOP 代理增强（若需要生成代理对象，会在此时完成）。

#### 举例：
```java
@Service
public class UserService implements InitializingBean {
    @Autowired
    private UserDao userDao;

    public UserService() {
        System.out.println("1. 实例化：构造器调用");
    }

    // 属性注入完成后执行（初始化阶段）
    @PostConstruct
    public void init() {
        System.out.println("3. 初始化：@PostConstruct 方法调用");
    }

    // 实现 InitializingBean 接口（初始化阶段）
    @Override
    public void afterPropertiesSet() throws Exception {
        System.out.println("4. 初始化：afterPropertiesSet 方法调用");
    }
}
```
- 执行顺序：`实例化（构造器）` → `属性注入（userDao 被赋值）` → `初始化（@PostConstruct → afterPropertiesSet）`；
- **未完成初始化**：指的是这些自定义逻辑尚未执行，对象可能还不满足“可用状态”（例如某些资源未初始化）。


### 四、总结：三个阶段的关系
```
实例化（new 出对象，调用构造器） → 属性注入（给字段赋值） → 初始化（执行自定义逻辑、AOP 代理）
```

- **已实例化但未完成属性注入和初始化**：  
  表示对象已经通过构造器创建（内存中存在），但依赖的属性（如 `userDao`）还没被注入（仍是 `null`），且初始化方法（如 `@PostConstruct`）也没执行。此时的对象是“半成品”，无法正常工作，但 Spring 可以提前将其暴露（放入二级/三级缓存），解决循环依赖问题。

比如在循环依赖中，A 实例化后，还没注入 B，也没执行初始化方法，但 Spring 可以先把这个“半成品 A”给 B 用，等 B 完成后再回头给 A 注入 B，最终完成 A 的初始化。这就是三级缓存解决循环依赖的核心逻辑。