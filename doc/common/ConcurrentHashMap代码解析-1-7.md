### ConcurrentHashMap 默认情况下，segment中的HashEntry<K,V>[]的大小是多少？

在 **JDK 7 及之前的版本** 中，`ConcurrentHashMap` 基于 `Segment` 分段锁实现，其中每个 `Segment` 内部维护一个 `HashEntry<K,V>[]` 数组（哈希桶）。

默认情况下，`HashEntry<K,V>[]` 的初始大小为 **2**（即初始容量为 2）。


### 补充说明：
1. **Segment 与 HashEntry 数组的关系**：  
   `ConcurrentHashMap` 初始化时默认会创建 **16 个 Segment**（由 `concurrencyLevel` 控制，默认值 16），每个 Segment 相当于一个独立的小哈希表，内部的 `HashEntry[]` 初始大小为 2。

2. **扩容机制**：  
   当某个 Segment 中的元素数量超过其阈值（`容量 × 负载因子`，负载因子默认 0.75）时，该 Segment 会单独扩容（将 `HashEntry[]` 数组长度翻倍），不影响其他 Segment，从而减少并发冲突。

3. **JDK 8 及之后的变化**：  
   JDK 8 彻底废弃了 `Segment` 分段锁，改用 `CAS + synchronized` 实现并发控制，内部结构变为 `Node[]` 数组（类似 `HashMap`），因此不再有 `HashEntry<K,V>[]` 与 `Segment` 的关联。


综上，**JDK 7 及之前**，`ConcurrentHashMap` 中每个 Segment 的 `HashEntry<K,V>[]` 默认初始大小为 **2**。