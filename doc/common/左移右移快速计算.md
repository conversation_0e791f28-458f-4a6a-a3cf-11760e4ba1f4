### 如何快速计算

---

好的，这里有一个快速计算的方法，不需要每次都转换成二进制。

---

## **1. 左移 `<<`**
**规则：** `a << n` 等于 \( a \times 2^n \)。

- 例子：`1 << 2`  
  \[
  1 \times 2^2 = 1 \times 4 = 4
  \]
  直接得 **4**。

---

## **2. 右移 `>>`**
**规则：** `a >> n` 等于 \( \lfloor a \div 2^n \rfloor \)（向零取整，对于正数就是整除）。

- 例子：`100 >> 2`  
  \[
  100 \div 2^2 = 100 \div 4 = 25
  \]
  直接得 **25**。

---

### **快速口诀**
- **左移 n 位** = 乘以 \( 2^n \)
- **右移 n 位** = 除以 \( 2^n \) 取整（正数）

这样你就不用转二进制，直接口算或简单笔算即可。