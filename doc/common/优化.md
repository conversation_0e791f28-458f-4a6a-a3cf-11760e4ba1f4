### 1.7 put方法

```java
final V put(K key, int hash, V value, boolean onlyIfAbsent) {
    HashEntry<K,V> node = tryLock() ? null :
            scanAndLockForPut(key, hash, value);
    V oldValue;
    try {
        HashEntry<K,V>[] tab = table;
        int index = (tab.length - 1) & hash;
        HashEntry<K,V> first = entryAt(tab, index);
        for (HashEntry<K,V> e = first;;) {
            if (e != null) {
                K k;
                if ((k = e.key) == key ||
                        (e.hash == hash && key.equals(k))) {
                    oldValue = e.value;
                    if (!onlyIfAbsent) {
                        e.value = value;
                        ++modCount;
                    }
                    break;
                }
                e = e.next;
            }
            else {
                if (node != null)
                    node.setNext(first);
                else
                    node = new HashEntry<K,V>(hash, key, value, first);
                int c = count + 1;
                if (c > threshold && tab.length < MAXIMUM_CAPACITY)
                    rehash(node);
                else
                    setEntryAt(tab, index, node);
                ++modCount;
                count = c;
                oldValue = null;
                break;
            }
        }
    } finally {
        unlock();
    }
    return oldValue;
}
```

这段代码


```java
if ((k = e.key) == key ||
    (e.hash == hash && key.equals(k))) {
```

这段代码是在链表中查找相同key的逻辑，用了两层判断来优化性能：

逻辑分解
第一层：引用相等性检查

```java
(k = e.key) == key
```

先将 e.key 赋值给局部变量 k
然后用 == 比较引用是否相同
如果是同一个对象引用，直接返回true

第二层：哈希值和内容相等性检查

```java
(e.hash == hash && key.equals(k))
```
先比较hash值是否相等（快速过滤）
再调用 equals() 方法比较内容

为什么这样设计？
1. 性能优化
```java
// 快 → 慢 的检查顺序
引用比较(==) → hash比较 → equals()比较
```

避免重复计算

```java
k = e.key  // 只取一次，避免多次访问volatile字段
```

短路求值

```java
// 如果引用相同，后面的判断都不执行
if (k == key || ...)  // k == key 为true时，直接成功
```

实际例子

```java
String key1 = "hello";
String key2 = "hello"; 
String key3 = new String("hello");

// 情况1：引用相同
key1 == key1  // true，直接匹配成功

// 情况2：引用不同但内容相同  
key1 == key3  // false，继续检查hash和equals
key1.equals(key3)  // true，匹配成功
```



