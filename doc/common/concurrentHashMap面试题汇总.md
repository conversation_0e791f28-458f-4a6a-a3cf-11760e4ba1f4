### 上边的12个问题，都如何回答？

好的，针对上面列出的12个常见问题，我为您提供详细的参考答案。这些答案基于 JDK 1.8 的核心设计，并会对比 JDK 1.7。

---

### 1. 简单介绍一下 ConcurrentHashMap。

**回答要点：**
*   **定位**：它是 JUC 包下的一个高性能、线程安全的哈希表。
*   **核心目标**：在保证线程安全的同时，提供更高的并发性能，解决 `Hashtable` 和 `Collections.synchronizedMap` 因全局锁导致的性能瓶颈。
*   **版本演进**：
    *   **JDK 1.7**：采用**分段锁**机制，将数据分成多个段，每段一把锁，锁粒度较粗。
    *   **JDK 1.8**：摒弃了分段锁，改用 **`synchronized` + `CAS` + `volatile`** 机制，锁粒度细化到**每个数组桶的首节点**，并发度大大提高，数据结构也与 `HashMap` 保持一致（数组+链表/红黑树）。

---

### 2. 能详细说说 JDK 1.7 和 JDK 1.8 中 ConcurrentHashMap 的区别吗？

| 特性 | JDK 1.7 | JDK 1.8 |
| :--- | :--- | :--- |
| **数据结构** | **Segment 数组 + HashEntry 数组 + 链表** | **Node 数组 + 链表 / 红黑树** |
| **锁机制** | **分段锁 (ReentrantLock)** | **`synchronized` (锁桶头节点) + `CAS`** |
| **锁粒度** | 段 (Segment)，默认16个，并发度受限 | **桶 (Bucket)**，锁粒度更细，并发度更高 |
| **并发度** | 由 Segment 数量决定，初始化后固定 | 理论上可达到 Node 数组的长度 |
| **时间复杂度** | 查询需要两次Hash，稍慢 | 与HashMap相同，在链表过长时转为红黑树，保证O(log n) |

---

### 3. ConcurrentHashMap 在 JDK 1.8 中为什么使用 Synchronized 锁而不是 ReentrantLock？

*   **锁粒度降低**：在 1.8 中，锁的仅仅是单个桶的头节点。在绝大多数情况下，发生哈希冲突的概率不高，锁竞争的强度很小。在这种低竞争场景下，JVM 对 `synchronized` 的优化（如锁升级：偏向锁->轻量级锁）效果非常好，其性能与 `ReentrantLock` 相差无几，甚至更优。
*   **减少内存开销**：`ReentrantLock` 是 API 级别的锁，需要维护一个 `AQS` 队列，占用更多内存。而 `synchronized` 是 JVM 内置的，由 JVM 进行管理，内存开销更小。
*   **简化开发**：使用内置锁可以减少代码的复杂性，避免手动获取和释放锁可能带来的错误。

---

### 4. ConcurrentHashMap 的 put 操作流程是怎样的？它是如何保证线程安全的？

**流程如下：**
1.  **计算 Hash**：计算 key 的 hash 值。
2.  **数组为空则初始化**：检查 table 是否为空，如果为空，使用 `CAS` 操作进行初始化。
3.  **定位桶并插入**：
    *   **情况一（桶为空）**：如果计算出的桶位置为 `null`，直接使用 `CAS` 无锁插入新节点。成功则结束，失败则说明有竞争，进入循环重试。
    *   **情况二（桶在迁移）**：如果桶的头节点是 `ForwardingNode`，说明正在扩容，则当前线程会协助进行数据迁移。
    *   **情况三（桶非空）**：使用 `synchronized` 锁住桶的头节点。
        *   如果是链表，则遍历链表，找到 key 则更新，没找到则尾插。
        *   如果是红黑树，则调用红黑树的插入方法。
4.  判断是否需要转红黑树：如果链表长度达到阈值（8），且数组容量达到最小树化容量（64），则将链表转化为红黑树。
5.  **更新计数**：调用 `addCount` 方法，使用 `CounterCell[]` 分片计数，尝试更新元素个数，并检查是否需要扩容。

**线程安全保证：**
*   **无锁操作**：对于初始化、空桶插入、计数更新，使用 `CAS`，保证原子性。
*   **有锁操作**：对于哈希冲突，使用 `synchronized` 锁住头节点，保证链表/树操作的原子性。
*   **可见性**：所有 Node 的 `val` 和 `next` 都用 `volatile` 修饰，保证修改后对其他线程立即可见。

---

### 5. ConcurrentHashMap 的 get 操作需要加锁吗？为什么？

**完全不需要加锁。**
原因如下：
*   `get` 操作访问的大多数变量（如 `Node.val` 和 `Node.next`）都被 `volatile` 关键字修饰。`volatile` 保证了变量的可见性，当一个线程修改了这些值，其他线程能立刻看到最新值。
*   整个 `get` 过程只是单纯的查询，不涉及任何写操作。在非阻塞算法中，纯读操作天生就是线程安全的。
*   这种无锁设计是 `get` 操作高性能的关键。

---

### 6. ConcurrentHashMap 的 size 方法是精确的吗？它是如何实现的？

**不是绝对精确的，它是一个估计值。**

**实现原理 (`size()` / `mappingCount()`)：**
1.  **基础计数器**：有一个 `baseCount` 字段。
2.  **分片计数器**：当出现并发竞争，线程 CAS 更新 `baseCount` 失败时，不会自旋，而是会命中一个 `CounterCell[]` 数组中的随机一个单元格，对该单元格进行 CAS 更新。
3.  **统计逻辑**：`size()` 方法最终返回的是 `baseCount` 加上所有 `CounterCell[i]` 的值的总和。

**为什么不是精确的？**
因为在并发环境下，在累加 `baseCount` 和各个 `CounterCell` 的瞬间，可能还有其他线程正在更新它们。这个统计过程没有用锁来冻结整个表，所以得到的是一个**弱一致性的快照**，但性能极高。

---

### 7. 讲一下 ConcurrentHashMap 的扩容机制。它如何实现多线程协同扩容？

**触发条件：**
*   元素数量达到 `容量 * 负载因子`。
*   某个链表的长度超过 8，但当前数组长度小于 64（此时会选择扩容而非树化）。

**扩容过程：**
1.  **构建新数组**：创建一个原来两倍大小的新数组 `nextTable`。
2.  **分配任务**：维护一个 `transferIndex` 变量，表示迁移的进度。从数组尾部开始向前分配迁移任务。
3.  **线程协助迁移**：
    *   第一个触发扩容的线程会开始迁移数据。
    *   当其他线程在进行 `put` 或 `remove` 操作时，如果发现当前操作的桶已经被一个 `ForwardingNode` 节点占位（表示该桶已迁移完毕或正在迁移），它不会阻塞等待，而是**主动帮助进行数据迁移**。
4.  **迁移完成**：所有桶都迁移完毕后，将 `nextTable` 设置为新的 `table`，完成扩容。

**优点**：这种“协同扩容”机制将扩容的压力分散到了多个线程上，大大缩短了扩容所需的时间，是一种“化整为零”的思想。

---

### 8. ConcurrentHashMap 为什么不允许 Key 和 Value 为 null？而 HashMap 却允许？

这主要是为了在并发环境下**避免歧义**。
*   **对于 `get(key)` 返回 `null`**：
    *   在 `HashMap` 中，`null` 可以表示 `key` 不存在，或者 `key` 存在但其对应的 `value` 就是 `null`。
    *   在并发场景下，如果允许 `null`，在调用 `map.get(key)` 得到 `null` 后，你无法区分是这个 `key` 没有被映射过，还是它刚刚被其他线程显式地置为了 `null`。这种二义性会导致复杂的业务逻辑错误。
*   **解决方案**：直接禁止 `null` 键值，这样一旦 `get(key)` 返回 `null`，就可以明确断定该 `key` 不存在于映射中。`HashMap` 在单线程下没有这个问题，所以允许 `null`。

---

### 9. ConcurrentHashMap 和 Hashtable 有什么区别？

| 特性 | ConcurrentHashMap | Hashtable |
| :--- | :--- | :--- |
| **锁机制** | **细粒度锁** (JDK1.8锁桶，1.7锁段) | **全局锁**，锁住整个对象 |
| **并发性能** | **高**，支持多线程同时读写（只要不冲突） | **极低**，同一时间只允许一个线程操作 |
| **Null 值** | **不允许** Key 和 Value 为 null | **不允许** Key 和 Value 为 null |
| **迭代器** | **弱一致性**，不会抛 `ConcurrentModificationException` | **强一致性**，但在迭代期间修改会抛 `ConcurrentModificationException`（fail-fast） |
| **版本** | 1.5 引入，1.8 重大更新 | JDK 1.0 就存在，古老的类 |

---

### 10. 解释一下 ConcurrentHashMap 迭代器的弱一致性。

弱一致性是指迭代器在遍历时，**能反映创建迭代器那一刻或之前的数据状态，但无法反映创建之后其他线程对 Map 的更新**。

*   **它不会抛出 `ConcurrentModificationException`**。这是它与 `HashMap` 的 **fail-fast** 迭代器的核心区别。
*   **可能看到也可能看不到更新**：在迭代过程中，如果其他线程修改了 Map（增、删、改），迭代器可能看到这些修改，也可能看不到。JVM 不保证一定能看到。
*   **设计目的**：这种设计牺牲了数据的强一致性，换取了 `get` 和迭代操作的高性能，因为它们都无需加锁。

---

### 11. 如果我们想要一个绝对精确的 size 该怎么办？

在实际开发中，通常不需要绝对精确的 `size`，估计值已经足够。如果业务场景**必须**要求精确值，可以考虑以下方案：

1.  **使用替代方法**：官方推荐使用 `mappingCount()` 方法，它返回 `long` 类型，行为与 `size()` 一样（也是估计值），但更安全。首先确认这个估计值是否满足需求。
2.  **外部计数**：在应用层，使用一个 `AtomicLong` 计数器，在每次成功执行 `put` 和 `remove` 操作后，手动、原子地更新这个计数器。但这会增加开销，并且需要确保所有修改 Map 的地方都同步更新计数器，容易出错。
3.  **加锁遍历**：使用 `synchronized` 锁住整个 Map，然后调用 `size()` 或遍历所有元素计数。这会导致性能急剧下降，完全失去了使用 `ConcurrentHashMap` 的意义。

**结论**：优先接受估计值。如果必须精确，应重新评估业务场景和选型是否合理。

---

### 12. 使用 `computeIfAbsent` 方法时需要注意什么？

`computeIfAbsent` 是一个强大的原子方法：”如果 key 不存在，则使用 mappingFunction 计算 value 并放入；如果 key 存在，则直接返回当前 value“。

**最大的注意事项是：在 `mappingFunction` 中不要修改当前 Map 本身！**

**原因：**
*   该方法本身已经持有了当前桶的锁。如果在映射函数中又尝试去 `put`、`remove` 同一个或相关的 key，可能会导致**死锁**或无限递归。
*   例如：`map.computeIfAbsent("A", k -> map.put("B", "value"))`，如果另一个线程正在对 "B" 进行操作，就可能构成死锁条件。

**最佳实践**：确保映射函数是一个”纯函数“，只根据输入的 `key` 进行计算，不产生任何副作用（尤其是不要修改外部状态，包括这个 Map 本身）。