```java
private HashEntry<K,V> scanAndLockForPut(K key, int hash, V value) {
            HashEntry<K,V> first = entryForHash(this, hash);
            HashEntry<K,V> e = first;
            HashEntry<K,V> node = null;
            int retries = -1; // negative while locating node
            while (!tryLock()) {
                HashEntry<K,V> f; // to recheck first below
                if (retries < 0) {
                    if (e == null) {
                        if (node == null) // speculatively create node
                            node = new HashEntry<K,V>(hash, key, value, null);
                        retries = 0;
                    }
                    else if (key.equals(e.key))
                        retries = 0;
                    else
                        e = e.next;
                }
                else if (++retries > MAX_SCAN_RETRIES) {
                    lock();
                    break;
                }
                else if ((retries & 1) == 0 &&
                         (f = entryForHash(this, hash)) != first) {
                    e = first = f; // re-traverse if entry changed
                    retries = -1;
                }
            }
            return node;
        }
```

---

这段代码来自 `ConcurrentHashMap` 内部的一个核心方法（JDK 7 及之前版本的实现），作用是**在添加键值对时，扫描对应哈希桶并尝试获取锁**，保证并发安全性。下面逐步解析其逻辑：


### 核心目标
- 找到 key 对应的哈希桶（链表），判断是否需要新增节点（key 不存在）或更新节点（key 已存在）。
- 在并发场景下，通过「自旋重试」尝试获取锁，减少阻塞开销；若重试次数过多，则直接阻塞等待锁。
- 最终返回需要插入的新节点（如果 key 不存在）。


### 关键变量
- `first`：当前哈希桶（链表）的头节点（初始值）。
- `e`：遍历链表的指针。
- `node`：待插入的新节点（仅当 key 不存在时创建）。
- `retries`：重试计数器（负数表示正在定位节点，非负数表示已开始重试获取锁）。
- `MAX_SCAN_RETRIES`：最大自旋重试次数（避免无限自旋浪费 CPU）。


### 逻辑拆解（循环 + 分支）
整个逻辑围绕 `while (!tryLock())` 展开：**不断尝试获取锁，直到成功或转为阻塞**。

#### 1. 初始阶段（`retries < 0`：定位节点）
首次进入循环时 `retries = -1`，此时不重试，而是先定位 key 在链表中的位置：
- 若 `e == null`：说明遍历到链表尾部，key 不存在，创建新节点 `node`，并将 `retries` 设为 0（进入重试阶段）。
- 若 `key.equals(e.key)`：找到相同 key 的节点，无需创建新节点，`retries` 设为 0（进入重试阶段）。
- 否则：继续遍历下一个节点（`e = e.next`）。

#### 2. 重试阶段（`retries >= 0`：尝试获取锁）
当 `retries` 转为 0 后，进入自旋重试逻辑：
- 若 `++retries > MAX_SCAN_RETRIES`：自旋次数超过上限，直接调用 `lock()` 阻塞等待锁（避免 CPU 空转），跳出循环。
- 若 `(retries & 1) == 0 && (f = entryForHash(this, hash)) != first`：
    - 每偶数次重试时，检查哈希桶的头节点是否变化（`f != first`）。
    - 若头节点变了（说明其他线程修改了链表，如插入/删除节点），则重新初始化遍历指针（`e = first = f`），并重置 `retries = -1`（重新定位节点）。


### 最终结果
当 `tryLock()` 成功获取锁后，循环结束，返回 `node`：
- 若 key 不存在，`node` 是待插入的新节点。
- 若 key 已存在，`node` 为 `null`（后续会更新已有节点的值）。


### 设计亮点
- **先扫描后加锁**：避免获取锁后再遍历的性能开销，利用自旋重试减少阻塞。
- **并发感知**：通过检查头节点是否变化，处理其他线程对链表的修改，保证遍历的正确性。
- **自适应策略**：自旋次数有限，超过阈值后转为阻塞，平衡 CPU 利用率和阻塞开销。

这段代码本质是 `ConcurrentHashMap` 实现「分段锁」机制的关键逻辑，通过细粒度锁（每个哈希桶一把锁）提升并发性能。