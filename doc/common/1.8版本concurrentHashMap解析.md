```java
public V get(Object key) {
        Node<K,V>[] tab; Node<K,V> e, p; int n, eh; K ek;
        int h = spread(key.hashCode());
        if ((tab = table) != null && (n = tab.length) > 0 &&
            (e = tabAt(tab, (n - 1) & h)) != null) {
            if ((eh = e.hash) == h) {
                if ((ek = e.key) == key || (ek != null && key.equals(ek)))
                    return e.val;
            }
            else if (eh < 0)
                return (p = e.find(h, key)) != null ? p.val : null;
            while ((e = e.next) != null) {
                if (e.hash == h &&
                    ((ek = e.key) == key || (ek != null && key.equals(ek))))
                    return e.val;
            }
        }
        return null;
    }
```

解析为：

```java
public V get(Object key) {
    int h = spread(key.hashCode());  // 计算hash值
    if ((tab = table) != null &&
            (e = tabAt(tab, (n - 1) & h)) != null) {  // 定位到桶

        if ((eh = e.hash) == h) {  // 检查第一个节点
            // 快速路径：第一个节点就匹配
        }
        else if (eh < 0) {
            // 特殊节点（TreeBin等）
        }
        // 遍历链表的其他节点...
    }
}
```