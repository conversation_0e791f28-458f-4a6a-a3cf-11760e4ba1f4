### concurrentHashMap面试可能问到的知识点

---

好的，ConcurrentHashMap 是 Java 并发面试中绝对的重中之重。下面我为你梳理一个全面的知识点清单，包括从基础到高级的常见问题。

---

### 一、核心设计与演进（JDK 1.7 vs JDK 1.8）

这是最常被问到的起点，用来考察你是否了解其底层原理的演变。

**JDK 1.7：**
*   **数据结构**： **Segment + HashEntry 数组**。本质上是一个分段锁的哈希表。
*   **锁机制**： **分段锁（Segment Lock）**。整个 Map 被分为多个 Segment（默认16个），每个 Segment 都是一个独立的、可重入的锁（ReentrantLock）。对不同的 Segment 进行操作时，不会发生锁竞争。
*   **优点**： 相比于 HashTable 的全局锁，并发粒度更细，性能更高。
*   **缺点**：
    *   并发度受限于 Segment 的数量，初始化后不能扩容。
    *   数据结构相对复杂，查询需要两次哈希定位。

**JDK 1.8：**
*   **数据结构**： **Node 数组 + 链表 / 红黑树**。与 HashMap 1.8 结构类似。
*   **锁机制**： **Synchronized + CAS + volatile**。
    *   **CAS**： 用于无竞争情况下的节点插入、计数更新等。
    *   **Synchronized**： 只在发生哈希冲突，需要对链表或红黑树进行操作时，才**锁定当前哈希桶（即数组的第一个节点）**。锁粒度从“段”细化到了“桶”，并发度大大提高。
*   **优点**：
    *   锁粒度更细，并发性能更高。
    *   数据结构更简单，与 HashMap 统一。
    *   引入了红黑树，防止链表过长导致性能下降。

---

### 二、关键实现细节（JDK 1.8 为重点）

#### 1. 如何保证线程安全？（CAS + Synchronized）
*   **初始化/扩容**： 使用 `volatile` 变量（`sizeCtl`）和 CAS 操作来控制，保证只有一个线程能进行初始化或扩容。
*   **put 操作**：
    *   如果当前桶为空，使用 CAS 无锁插入。
    *   如果桶不为空，则使用 `synchronized` 锁住桶的头节点，再进行链表或红黑树的操作。
*   **get 操作**：
    *   完全无锁。因为 Node 的 `val` 和 `next` 属性都被 `volatile` 修饰，保证了线程间的可见性。

#### 2. size() 方法是如何实现的？
*   **不直接维护一个全局的 size 计数器**，因为在并发环境下维护一个绝对精确的计数器代价很高。
*   **采用分片计数**： 内部维护一个 `CounterCell[]` 数组。当出现竞争时，线程会尝试更新自己对应的 `CounterCell`。
*   **最终结果**： `size()` 方法返回的是一个**估计值**，它通过累加 `baseCount` 和所有 `CounterCell` 的值来得到。它**不是 100% 精确**的，但在高并发下性能远好于精确计数。

#### 3. 扩容机制（重点与难点）
*   **触发条件**： 当元素数量超过 `容量 * 负载因子` 时，或者某个链表长度超过 8 但数组长度小于 64 时，会触发扩容。
*   **如何扩容**：
    *   创建一个新的、容量为原来两倍的 Node 数组。
    *   **多线程协助扩容**： 这是一个精妙的设计。当有线程进行 `put` 或 `remove` 操作时，如果发现当前桶正处于迁移状态（`ForwardingNode`），它不会阻塞等待，而是**协助其他线程一起完成数据迁移**。这大大提高了扩容效率。
    *   **迁移单位**： 以桶为单位进行迁移，从后向前推进。

#### 4. 为什么用 Synchronized 代替 ReentrantLock？
*   **锁粒度降低**： 在 1.8 中，锁的粒度是单个哈希桶，竞争概率已经很低。此时 Synchronized 的性能与 ReentrantLock 相差无几，甚至更好。
*   **内存开销**： Synchronized 是 JVM 内置锁，随着锁升级（偏向锁->轻量级锁->重量级锁）优化，而 ReentrantLock 需要额外的 API 操作和内存开销。
*   **简化代码**： 使用内置锁可以减少代码复杂性。

---

### 三、与其他容器的对比

#### 1. ConcurrentHashMap vs Hashtable
*   **锁粒度**： Hashtable 使用全局锁，操作整个数组，效率极低。ConcurrentHashMap 使用分段锁或桶锁，并发性能高。
*   **Null 值**： 两者都不允许 Key 或 Value 为 null，以避免在并发环境下出现歧义（无法区分是不存在还是值为 null）。

#### 2. ConcurrentHashMap vs Collections.synchronizedMap(Map)
*   `synchronizedMap` 使用一个**统一的互斥锁**来包装传入的 Map，其锁粒度与 Hashtable 一样粗。
*   `ConcurrentHashMap` 的并发性能远高于它。

#### 3. ConcurrentHashMap vs HashMap
*   **线程安全**： 最根本的区别。
*   **Null 值**： HashMap 允许一个 null key 和多个 null value，而 ConcurrentHashMap 不允许。

---

### 四、高级特性与使用场景

#### 1. 原子性操作方法
*   `putIfAbsent(K key, V value)`： 如果 key 不存在则放入，存在则返回当前值。
*   `compute(K key, BiFunction remappingFunction)`： 原子性地计算并更新指定 key 的值。
*   `merge(K key, V value, BiFunction remappingFunction)`： 原子性地合并操作。
*   `getOrDefault`： 安全地获取值，如果不存在则返回默认值。

**面试点**： 这些方法保证了整个“检查-计算-写入”过程的原子性，避免了使用 `get` 和 `put` 组合时可能出现的竞态条件。

#### 2. 遍历的弱一致性
*   **迭代器（Iterator）** 是**弱一致性的**。
*   当创建迭代器时，或在进行迭代时，它可能会反映也可能不会反映创建之后的所有更新。
*   它**不会抛出 `ConcurrentModificationException`**。这是它与 `HashMap` 的 `fail-fast` 迭代器的重要区别。

---

### 五、常见面试问题清单

1.  **简单介绍一下 ConcurrentHashMap。**
2.  **能详细说说 JDK 1.7 和 JDK 1.8 中 ConcurrentHashMap 的区别吗？**
3.  **ConcurrentHashMap 在 JDK 1.8 中为什么使用 Synchronized 锁而不是 ReentrantLock？**
4.  **ConcurrentHashMap 的 put 操作流程是怎样的？它是如何保证线程安全的？**
5.  **ConcurrentHashMap 的 get 操作需要加锁吗？为什么？**
6.  **ConcurrentHashMap 的 size 方法是精确的吗？它是如何实现的？**
7.  **讲一下 ConcurrentHashMap 的扩容机制。它如何实现多线程协同扩容？**
8.  **ConcurrentHashMap 为什么不允许 Key 和 Value 为 null？而 HashMap 却允许？**
9.  **ConcurrentHashMap 和 Hashtable 有什么区别？**
10. **解释一下 ConcurrentHashMap 迭代器的弱一致性。**
11. **如果我们想要一个绝对精确的 size 该怎么办？**（可以讨论使用 `mappingCount()` 方法，或者在外围用 AtomicLong 自己维护，但会牺牲性能）
12. **使用 `computeIfAbsent` 方法时需要注意什么？**（注意映射函数不应修改 Map 本身，否则可能死锁）

---

### 总结

准备 ConcurrentHashMap 的面试，核心是理解其 **“分段锁”到“桶锁”的设计演进**，以及 **CAS + Synchronized 如何协同工作** 来实现高效并发。同时，对 `size()`、扩容、迭代器等细节的理解，能很好地体现你的深度。建议结合源码（至少是核心方法的源码）来加深理解。