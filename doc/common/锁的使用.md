```java
public class FixedDemo {
    private volatile int stock = 1;
    private final Object lock = new Object(); // 锁对象
    
    public void purchase() {
        synchronized (lock) { // 加锁保证原子性
            if (stock > 0) {
                try { Thread.sleep(100); } catch (InterruptedException e) {}
                stock--;
                System.out.println(Thread.currentThread().getName() + " 购买成功");
            } else {
                System.out.println(Thread.currentThread().getName() + " 库存不足");
            }
        }
    }
}
```

private final Object lock = new Object(); // 锁对象
请问这是什么加锁方式？purchase方法上不能直接增加synchronized 关键字吗？

---

非常好的问题！这涉及到`synchronized`使用的几种方式及其区别。

## 当前的加锁方式：对象监视器锁

```java
private final Object lock = new Object(); // 锁对象

public void purchase() {
    synchronized (lock) { // 使用专门的锁对象
        // 临界区代码
    }
}
```

**这种方式的特点：**
- 使用一个专门的`Object`实例作为锁
- 粒度更细，更灵活
- 推荐的做法

## 为什么不在方法上直接加`synchronized`？

可以在方法上加`synchronized`，但这两种方式有重要区别：

### 1. **锁的对象不同**

```java
// 方式1：实例方法上加synchronized
public synchronized void purchase() {
    // 锁的是当前实例对象(this)
}

// 方式2：使用专门的锁对象
private final Object lock = new Object();
public void purchase() {
    synchronized (lock) {  // 锁的是lock对象
        // ...
    }
}
```

### 2. **锁的粒度不同**

**方法级`synchronized`的问题：**
```java
public class Store {
    private volatile int stockA = 10;
    private volatile int stockB = 20;
    
    // 两个方法使用同一把锁(this)
    public synchronized void purchaseA() {
        if (stockA > 0) stockA--;
    }
    
    public synchronized void purchaseB() {
        if (stockB > 0) stockB--;
    }
}
```
- 购买商品A和购买商品B会相互阻塞！
- 实际上它们操作的是不同的库存，不需要互斥

**使用专门锁对象的优势：**
```java
public class Store {
    private volatile int stockA = 10;
    private volatile int stockB = 20;
    private final Object lockA = new Object();
    private final Object lockB = new Object();
    
    public void purchaseA() {
        synchronized (lockA) {  // 只锁A相关操作
            if (stockA > 0) stockA--;
        }
    }
    
    public void purchaseB() {
        synchronized (lockB) {  // 只锁B相关操作
            if (stockB > 0) stockB--;
        }
    }
}
```
- 购买A和购买B可以并发执行，性能更好
- 锁粒度更细，减少不必要的阻塞

### 3. **灵活性对比**

| 特性 | 方法级`synchronized` | 专用锁对象 |
|------|---------------------|------------|
| **锁对象** | `this`（当前实例） | 任意对象 |
| **锁粒度** | 粗（整个方法互斥） | 细（可自定义） |
| **灵活性** | 低 | 高 |
| **继承问题** | 子类可能意外影响 | 更安全 |
| **性能** | 可能较差 | 更优 |

## 实际应用场景

### 场景1：读写分离（更细粒度）
```java
public class ReadWriteCounter {
    private int value = 0;
    private final Object readLock = new Object();
    private final Object writeLock = new Object();
    
    // 多个读操作可以并发
    public int getValue() {
        synchronized (readLock) {
            return value;
        }
    }
    
    // 写操作需要互斥
    public void increment() {
        synchronized (writeLock) {
            value++;
        }
    }
}
```

### 场景2：避免外部同步干扰
```java
public class BankAccount {
    private int balance = 1000;
    private final Object balanceLock = new Object();
    
    public void transfer(int amount) {
        synchronized (balanceLock) {  // 使用内部锁，不受外部影响
            balance += amount;
        }
    }
    
    // 外部代码无法干扰我们的同步
    // 如果使用synchronized方法，外部可以通过 synchronized(account) 干扰
}
```

### 场景3：条件等待
```java
public class MessageQueue {
    private final List<String> messages = new ArrayList<>();
    private final Object lock = new Object();  // 用于wait/notify
    
    public void send(String message) {
        synchronized (lock) {
            messages.add(message);
            lock.notifyAll();  // 需要同一个对象
        }
    }
    
    public String receive() throws InterruptedException {
        synchronized (lock) {
            while (messages.isEmpty()) {
                lock.wait();  // 需要同一个对象
            }
            return messages.remove(0);
        }
    }
}
```

## 什么时候适合用方法级`synchronized`？

```java
// 适合使用synchronized方法的情况：
public class SimpleCounter {
    private int count = 0;
    
    // 简单场景，只有一个共享资源
    public synchronized void increment() {
        count++;
    }
    
    public synchronized int getCount() {
        return count;
    }
}
```

**适用条件：**
- 类很简单，只有一个需要同步的资源
- 不需要细粒度的锁控制
- 代码简单，易于理解

## 总结

1. **专用锁对象`private final Object lock`是更专业、更灵活的做法**
2. **方法级`synchronized`是快捷方式，但粒度较粗**
3. **在复杂并发场景中，专用锁对象可以提供更好的性能和更细的控制**
4. **选择依据：根据业务场景的复杂度和性能要求来决定**

在你的例子中，使用专用锁对象是很好的实践！

---

### 如果使用synchronized方法，外部可以通过 synchronized(account) 干扰  ///啥意思？

这个问题非常好！让我用一个具体的例子来解释这种"外部干扰"是什么意思。

## 问题演示：外部同步干扰

```java
class BankAccount {
    private int balance = 1000;
    
    // 使用synchronized方法，锁的是this（当前账户对象）
    public synchronized void transfer(int amount) {
        System.out.println(Thread.currentThread().getName() + " 开始转账: " + amount);
        // 模拟一些处理时间
        try { Thread.sleep(1000); } catch (InterruptedException e) {}
        balance += amount;
        System.out.println(Thread.currentThread().getName() + " 转账完成，余额: " + balance);
    }
    
    public synchronized int getBalance() {
        return balance;
    }
}

public class ExternalInterferenceDemo {
    public static void main(String[]s) throws InterruptedException {
        BankAccount account = new BankAccount();
        
        // 正常的转账线程
        Thread normalTransfer = new Thread(() -> {
            account.transfer(100);  // 内部使用synchronized(this)
        }, "正常转账线程");
        
        // 恶意的外部代码 - 也锁同一个account对象
        Thread maliciousThread = new Thread(() -> {
            synchronized (account) {  // 外部锁住了同一个对象！
                System.out.println("恶意线程获得了账户锁，正在阻塞转账操作...");
                try {
                    Thread.sleep(5000);  // 长时间持有锁
                } catch (InterruptedException e) {}
                System.out.println("恶意线程释放锁");
            }
        }, "恶意线程");
        
        maliciousThread.start();
        Thread.sleep(100);  // 确保恶意线程先获得锁
        normalTransfer.start();
        
        maliciousThread.join();
        normalTransfer.join();
    }
}
```

**运行结果：**
```
恶意线程获得了账户锁，正在阻塞转账操作...
（等待5秒...）
恶意线程释放锁
正常转账线程 开始转账: 100
正常转账线程 转账完成，余额: 1100
```

## 问题分析

### 发生了什么？
1. **恶意线程**通过`synchronized(account)`获得了锁
2. **正常转账线程**调用`transfer()`方法时，内部的`synchronized`也在尝试锁`this`（同一个对象）
3. 恶意线程长时间持有锁（5秒），导致正常的转账操作被阻塞
4. 这就是"外部干扰" - 外部代码可以故意或无意地干扰内部的同步机制

## 解决方案：使用私有锁对象

```java
class SafeBankAccount {
    private int balance = 1000;
    // 使用私有final锁对象，外部无法访问
    private final Object lock = new Object();
    
    public void transfer(int amount) {
        synchronized (lock) {  // 使用内部私有锁
            System.out.println(Thread.currentThread().getName() + " 开始转账: " + amount);
            try { Thread.sleep(1000); } catch (InterruptedException e) {}
            balance += amount;
            System.out.println(Thread.currentThread().getName() + " 转账完成，余额: " + balance);
        }
    }
    
    public int getBalance() {
        synchronized (lock) {
            return balance;
        }
    }
}

public class SafeDemo {
    public static void main(String[] args) throws InterruptedException {
        SafeBankAccount account = new SafeBankAccount();
        
        Thread normalTransfer = new Thread(() -> {
            account.transfer(100);
        }, "正常转账线程");
        
        Thread maliciousThread = new Thread(() -> {
            // 外部代码无法干扰，因为锁对象是私有的！
            synchronized (account) {  // 这已经没用了，锁的不是同一个对象
                System.out.println("恶意线程尝试干扰...但失败了");
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {}
            }
        }, "恶意线程");
        
        maliciousThread.start();
        Thread.sleep(100);
        normalTransfer.start();
        
        maliciousThread.join();
        normalTransfer.join();
    }
}
```

**运行结果：**
```
恶意线程尝试干扰...但失败了
正常转账线程 开始转账: 100
正常转账线程 转账完成，余额: 1100
（两个操作几乎同时完成，互不干扰）
```

## 关键区别对比

| 方面 | `synchronized`方法（锁this） | 私有锁对象 |
|------|-----------------------------|------------|
| **锁对象** | `this`（公开可访问） | `private final Object lock`（外部不可见） |
| **外部干扰** | 可能被干扰 | 不可能被干扰 |
| **封装性** | 差（暴露同步机制） | 好（隐藏实现细节） |
| **安全性** | 较低 | 较高 |

## 现实中的风险场景

### 场景1：无意死锁
```java
// 开发人员A写的代码
class AccountA {
    public synchronized void methodA() {
        // 一些操作
    }
}

// 开发人员B写的代码（不知道A的实现）
class ServiceB {
    public void process(AccountA account) {
        synchronized (account) {  // 无意中使用了同一个锁
            // 处理账户...
            account.methodA();  // 死锁风险！
        }
    }
}
```

### 场景2：恶意拒绝服务
```java
// 攻击代码
public class DoSAttack {
    public static void attack(BankAccount target) {
        // 长时间锁定目标账户，使其无法操作
        synchronized (target) {
            while (true) {
                // 永久持有锁...
            }
        }
    }
}
```

## 最佳实践总结

1. **优先使用私有锁对象**：
   ```java
   private final Object lock = new Object();
   public void method() {
       synchronized (lock) {
           // 临界区
       }
   }
   ```

2. **避免使用`synchronized`方法**在可能被外部使用的API中

3. **如果需要使用`this`作为锁，请明确文档说明**，让使用者知道同步策略

4. **在类库设计中，私有锁是更安全的选择**

这样就能确保你的同步机制不会被外部代码意外或恶意地干扰！

---

### ReentrantLock能用吗？

**当然能用！而且`ReentrantLock`在很多场景下是更好的选择！**

`ReentrantLock`提供了比`synchronized`更丰富的功能，是Java并发包中非常重要的锁实现。

## 基本用法对比

### 使用 `ReentrantLock` 改造你的例子

```java
import java.util.concurrent.locks.ReentrantLock;

public class BankAccount {
    private int balance = 1000;
    private final ReentrantLock lock = new ReentrantLock();  // ReentrantLock替代synchronized
    
    public void transfer(int amount) {
        lock.lock();  // 手动获取锁
        try {
            System.out.println(Thread.currentThread().getName() + " 开始转账: " + amount);
            try { Thread.sleep(1000); } catch (InterruptedException e) {}
            balance += amount;
            System.out.println(Thread.currentThread().getName() + " 转账完成，余额: " + balance);
        } finally {
            lock.unlock();  // 必须在finally中释放锁！
        }
    }
    
    public int getBalance() {
        lock.lock();
        try {
            return balance;
        } finally {
            lock.unlock();
        }
    }
}
```

## `ReentrantLock` 的优势特性

### 1. **尝试获取锁（避免死锁）**

```java
public boolean tryTransfer(int amount) {
    if (lock.tryLock()) {  // 尝试获取锁，立即返回
        try {
            // 获取锁成功，执行操作
            balance += amount;
            return true;
        } finally {
            lock.unlock();
        }
    } else {
        // 获取锁失败，执行备用方案
        System.out.println("系统繁忙，请稍后重试");
        return false;
    }
}

// 或者带超时的尝试
public boolean tryTransferWithTimeout(int amount) throws InterruptedException {
    if (lock.tryLock(3, TimeUnit.SECONDS)) {  // 最多等待3秒
        try {
            balance += amount;
            return true;
        } finally {
            lock.unlock();
        }
    } else {
        System.out.println("操作超时");
        return false;
    }
}
```

### 2. **公平锁（避免线程饥饿）**

```java
// 创建公平锁，按照请求顺序获取锁
private final ReentrantLock fairLock = new ReentrantLock(true);

public void fairTransfer(int amount) {
    fairLock.lock();
    try {
        balance += amount;
    } finally {
        fairLock.unlock();
    }
}
```

### 3. **可中断的锁获取**

```java
public void interruptibleTransfer(int amount) throws InterruptedException {
    lock.lockInterruptibly();  // 可响应中断的锁获取
    try {
        // 长时间操作...
        Thread.sleep(10000);
        balance += amount;
    } finally {
        lock.unlock();
    }
}
```

### 4. **条件变量（精细的线程协调）**

这是`ReentrantLock`最强大的特性之一！

```java
public class AdvancedBankAccount {
    private int balance = 1000;
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition sufficientFunds = lock.newCondition();  // 条件变量
    private final Condition balanceChanged = lock.newCondition();
    
    // 取款 - 如果余额不足就等待
    public void withdraw(int amount) throws InterruptedException {
        lock.lock();
        try {
            while (balance < amount) {
                System.out.println(Thread.currentThread().getName() + " 余额不足，等待存款...");
                sufficientFunds.await();  // 释放锁并等待
            }
            balance -= amount;
            System.out.println("取款 " + amount + " 成功，余额: " + balance);
            balanceChanged.signalAll();  // 通知所有等待余额变化的线程
        } finally {
            lock.unlock();
        }
    }
    
    // 存款 - 存款后通知等待的取款线程
    public void deposit(int amount) {
        lock.lock();
        try {
            balance += amount;
            System.out.println("存款 " + amount + " 成功，余额: " + balance);
            sufficientFunds.signalAll();  // 通知所有等待充足资金的线程
        } finally {
            lock.unlock();
        }
    }
}
```

## 完整示例：生产者-消费者模式

```java
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

public class MessageQueue {
    private final String[] messages;
    private int count, putIndex, takeIndex;
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition notFull = lock.newCondition();   // 队列非满条件
    private final Condition notEmpty = lock.newCondition();  // 队列非空条件
    
    public MessageQueue(int capacity) {
        messages = new String[capacity];
    }
    
    public void put(String message) throws InterruptedException {
        lock.lock();
        try {
            while (count == messages.length) {
                notFull.await();  // 队列已满，等待
            }
            messages[putIndex] = message;
            putIndex = (putIndex + 1) % messages.length;
            count++;
            notEmpty.signal();  // 通知消费者有消息了
        } finally {
            lock.unlock();
        }
    }
    
    public String take() throws InterruptedException {
        lock.lock();
        try {
            while (count == 0) {
                notEmpty.await();  // 队列为空，等待
            }
            String message = messages[takeIndex];
            takeIndex = (takeIndex + 1) % messages.length;
            count--;
            notFull.signal();  // 通知生产者有空位了
            return message;
        } finally {
            lock.unlock();
        }
    }
}
```

## `ReentrantLock` vs `synchronized` 对比

| 特性 | `synchronized` | `ReentrantLock` |
|------|----------------|-----------------|
| **使用方式** | 关键字，自动释放锁 | API调用，手动释放 |
| **尝试锁** | 不支持 | `tryLock()`, `tryLock(timeout)` |
| **公平性** | 非公平 | 可选择公平或非公平 |
| **可中断** | 不支持 | `lockInterruptibly()` |
| **条件变量** | `wait()/notify()` | 多个`Condition`对象 |
| **性能** | JDK6后优化得很好 | 在高竞争下可能更好 |
| **代码复杂度** | 简单 | 相对复杂，需要try-finally |

## 使用建议

### 使用 `synchronized` 的情况：
- 简单的同步需求
- 代码量少，不想手动管理锁
- 不需要高级特性

### 使用 `ReentrantLock` 的情况：
- 需要尝试获取锁的功能
- 需要公平锁
- 需要可中断的锁获取
- 需要多个条件变量进行精细的线程协调
- 高竞争场景下的性能优化

## 总结

**`ReentrantLock`绝对能用，而且在复杂并发场景下是更好的选择！**

对于你的银行账户例子，如果只是简单的转账操作，`synchronized`可能就足够了。但如果需要处理余额不足等待、操作超时、公平性等复杂需求，`ReentrantLock`提供的丰富功能会让实现更加优雅和健壮。

选择依据：
- **简单场景** → `synchronized` 或 私有对象锁
- **复杂场景** → `ReentrantLock`
- **需要精细线程协调** → `ReentrantLock + Condition`

---

### 没看懂，“条件变量”能做什么用？请仔细讲一下
private final Condition sufficientFunds = lock.newCondition();  // 条件变量
private final Condition balanceChanged = lock.newCondition();

问得好！条件变量是`ReentrantLock`最强大的特性之一，它解决了`synchronized`中`wait()/notify()`的局限性。让我详细解释一下。

## 基础概念：什么是条件变量？

**条件变量**允许线程在某个条件不满足时**主动释放锁并等待**，当条件满足时再**被唤醒重新竞争锁**。

可以把条件变量想象成一个**专业的等待室**：
- 每个条件变量代表一个特定的等待条件
- 线程可以进入对应的等待室休息
- 当条件满足时，只唤醒在对应等待室中的线程

## 对比：`synchronized`的局限性

### 使用 `synchronized + wait()/notifyAll()`
```java
public class BasicAccount {
    private int balance = 1000;
    
    public synchronized void withdraw(int amount) throws InterruptedException {
        while (balance < amount) {
            wait();  // 所有等待线程都在同一个"等待室"
        }
        balance -= amount;
        notifyAll();  // 唤醒所有等待线程，不管它们在等什么
    }
    
    public synchronized void deposit(int amount) {
        balance += amount;
        notifyAll();  // 可能唤醒不该唤醒的线程
    }
}
```

**问题：**
- `notifyAll()`会唤醒所有等待的线程，包括可能在等待其他条件的线程
- 造成"虚假唤醒"，性能低下

## `ReentrantLock + Condition` 的解决方案

### 场景：银行账户的精细控制

```java
import java.util.concurrent.locks.*;

public class AdvancedBankAccount {
    private int balance = 1000;
    private final ReentrantLock lock = new ReentrantLock();
    
    // 创建两个专门的"等待室"
    private final Condition sufficientFunds = lock.newCondition();    // 资金充足等待室
    private final Condition balanceChanged = lock.newCondition();     // 余额变化等待室
    
    // 取款操作 - 只在"资金充足等待室"等待
    public void withdraw(int amount) throws InterruptedException {
        lock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 尝试取款: " + amount);
            
            while (balance < amount) {
                System.out.println(Thread.currentThread().getName() + " 余额不足，进入资金充足等待室...");
                sufficientFunds.await();  // 只进入"资金充足等待室"
            }
            
            balance -= amount;
            System.out.println(Thread.currentThread().getName() + " 取款成功，余额: " + balance);
            
            // 通知所有在"余额变化等待室"的线程
            balanceChanged.signalAll();
            
        } finally {
            lock.unlock();
        }
    }
    
    // 存款操作
    public void deposit(int amount) {
        lock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 存款: " + amount);
            balance += amount;
            System.out.println("存款后余额: " + balance);
            
            // 只唤醒在"资金充足等待室"的线程
            sufficientFunds.signalAll();
            
        } finally {
            lock.unlock();
        }
    }
    
    // 查询余额，如果余额没变化就等待
    public void waitForBalanceChange() throws InterruptedException {
        lock.lock();
        try {
            int currentBalance = balance;
            System.out.println(Thread.currentThread().getName() + " 开始监控余额变化，当前: " + currentBalance);
            
            while (balance == currentBalance) {
                System.out.println(Thread.currentThread().getName() + " 余额未变，进入余额变化等待室...");
                balanceChanged.await();  // 只进入"余额变化等待室"
            }
            
            System.out.println(Thread.currentThread().getName() + " 检测到余额变化! 新余额: " + balance);
            
        } finally {
            lock.unlock();
        }
    }
}
```

## 实际运行演示

```java
public class ConditionDemo {
    public static void main(String[] args) throws InterruptedException {
        AdvancedBankAccount account = new AdvancedBankAccount();
        
        // 线程1：尝试取款2000（余额不足，会等待）
        Thread withdrawThread = new Thread(() -> {
            try {
                account.withdraw(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, "取款线程");
        
        // 线程2：监控余额变化
        Thread monitorThread = new Thread(() -> {
            try {
                account.waitForBalanceChange();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, "监控线程");
        
        // 线程3：存款操作
        Thread depositThread = new Thread(() -> {
            try {
                Thread.sleep(1000);  // 等待1秒
                account.deposit(1500);  // 存款1500
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, "存款线程");
        
        withdrawThread.start();
        monitorThread.start();
        depositThread.start();
        
        withdrawThread.join();
        monitorThread.join();
        depositThread.join();
    }
}
```

**可能的输出：**
```
取款线程 尝试取款: 2000
取款线程 余额不足，进入资金充足等待室...
监控线程 开始监控余额变化，当前: 1000
监控线程 余额未变，进入余额变化等待室...
存款线程 存款: 1500
存款后余额: 2500
取款线程 取款成功，余额: 500
监控线程 检测到余额变化! 新余额: 500
```

## 关键优势分析

### 1. **精准唤醒**
- `sufficientFunds.signalAll()` 只唤醒等待**资金充足**的线程
- `balanceChanged.signalAll()` 只唤醒等待**余额变化**的线程
- 不会唤醒不相关的线程

### 2. **多个等待条件**
一个锁可以创建多个条件变量：
```java
// 更复杂的银行系统
private final Condition hasSufficientFunds = lock.newCondition();      // 资金充足
private final Condition balanceAboveThreshold = lock.newCondition();   // 余额超过阈值
private final Condition transactionCompleted = lock.newCondition();    // 交易完成
private final Condition auditRequired = lock.newCondition();          // 需要审计
```

### 3. **真实世界例子：阻塞队列**

```java
public class BlockingQueue<T> {
    private final T[] items;
    private int count, putIndex, takeIndex;
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition notFull = lock.newCondition();   // 队列不满条件
    private final Condition notEmpty = lock.newCondition();  // 队列不空条件
    
    public void put(T item) throws InterruptedException {
        lock.lock();
        try {
            while (count == items.length) {
                notFull.await();  // 队列满，等待"不满"条件
            }
            items[putIndex] = item;
            count++;
            notEmpty.signal();    // 通知消费者队列不空了
        } finally {
            lock.unlock();
        }
    }
    
    public T take() throws InterruptedException {
        lock.lock();
        try {
            while (count == 0) {
                notEmpty.await();  // 队列空，等待"不空"条件
            }
            T item = items[takeIndex];
            count--;
            notFull.signal();     // 通知生产者队列不满了
            return item;
        } finally {
            lock.unlock();
        }
    }
}
```

## Condition 的核心方法

| 方法 | 说明 |
|------|------|
| `await()` | 释放锁并等待，可被中断 |
| `awaitUninterruptibly()` | 释放锁并等待，不可被中断 |
| `awaitNanos(long)` | 带超时的等待（纳秒） |
| `await(long, TimeUnit)` | 带超时的等待 |
| `signal()` | 唤醒一个等待线程 |
| `signalAll()` | 唤醒所有等待线程 |

## 总结

**条件变量的核心价值：**

1. **精准控制**：每个条件变量代表一个特定的等待条件
2. **高效唤醒**：只唤醒真正需要被唤醒的线程
3. **代码清晰**：不同的等待条件用不同的条件变量，逻辑更清晰
4. **性能优化**：避免不必要的线程唤醒和竞争

在你的银行账户例子中：
- `sufficientFunds`：专门给"想取款但余额不足"的线程使用
- `balanceChanged`：专门给"等待余额变化"的线程使用

这样存款时只唤醒取款线程，取款时只唤醒监控线程，各司其职，效率更高！