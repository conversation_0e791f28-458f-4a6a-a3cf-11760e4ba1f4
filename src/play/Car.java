package play;

public class Car {
    private String engine;
    private String color;
    private int price;
    private int year;

    public Car() {
        this.engine = "V8";
        this.color = "Red";
        this.price = 100000;
        this.year = 2023;
    }

    @Override
    public String toString() {
        return "Car{" +
                "engine='" + engine + '\'' +
                ", color='" + color + '\'' +
                ", price=" + price +
                ", year=" + year +
                '}';
    }

    public static void main(String[] args) {
        int concurrencyLevel = 16;
        int initialCapacity = 16;
        final int MIN_SEGMENT_TABLE_CAPACITY = 2;
        final int MAXIMUM_CAPACITY = 1 << 30;

        final int segmentMask;
        final int segmentShift;

        int sshift = 0;
        int ssize = 1;
        while (ssize < concurrencyLevel) {
            ++sshift;
            ssize <<= 1;
        }
        segmentShift = 32 - sshift;
        segmentMask = ssize - 1;
        if (initialCapacity > MAXIMUM_CAPACITY)
            initialCapacity = MAXIMUM_CAPACITY;
        int c = initialCapacity / ssize;
        if (c * ssize < initialCapacity)
            ++c;
        int cap = MIN_SEGMENT_TABLE_CAPACITY;
        while (cap < c)
            cap <<= 1;
    }
}
